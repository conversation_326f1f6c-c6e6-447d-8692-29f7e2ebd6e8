#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Shapefile分析工具
使用Python标准库分析shapefile文件结构和基本信息
不需要geopandas等外部依赖，但功能相对有限
"""

import os
import struct
import glob
from pathlib import Path

def find_shapefile():
    """查找当前目录下的shapefile文件"""
    shp_files = glob.glob("*.shp")
    if not shp_files:
        print("在当前目录中未找到.shp文件")
        return None
    
    print(f"找到shapefile文件: {shp_files}")
    return shp_files[0]

def check_shapefile_components(shp_path):
    """检查shapefile的组成文件是否完整"""
    print(f"\n=== 检查Shapefile文件组成 ===")
    base_name = os.path.splitext(shp_path)[0]
    
    required_files = ['.shp', '.shx', '.dbf']
    optional_files = ['.prj', '.cpg', '.sbn', '.sbx', '.qix']
    
    missing_required = []
    missing_optional = []
    existing_files = []
    
    for ext in required_files:
        file_path = base_name + ext
        if os.path.exists(file_path):
            existing_files.append(ext)
            print(f"  ✅ {ext}: {os.path.getsize(file_path)} bytes")
        else:
            missing_required.append(ext)
    
    for ext in optional_files:
        file_path = base_name + ext
        if os.path.exists(file_path):
            existing_files.append(ext)
            print(f"  📁 {ext}: {os.path.getsize(file_path)} bytes")
        else:
            missing_optional.append(ext)
    
    if missing_required:
        print(f"❌ 缺少必需文件: {missing_required}")
        return False
    else:
        print("✅ 必需文件完整")
    
    if missing_optional:
        print(f"⚠️  缺少可选文件: {missing_optional}")
        if '.prj' in missing_optional:
            print("   警告: 缺少.prj文件可能导致投影系统问题！")
    
    return True

def read_shp_header(shp_path):
    """读取SHP文件头信息"""
    print(f"\n=== 分析SHP文件头 ===")
    
    try:
        with open(shp_path, 'rb') as f:
            # 读取文件头 (100字节)
            header = f.read(100)
            
            # 文件代码 (字节0-3, 大端序)
            file_code = struct.unpack('>I', header[0:4])[0]
            if file_code != 9994:
                print(f"❌ 无效的shapefile文件代码: {file_code}")
                return None
            else:
                print(f"✅ 文件代码正确: {file_code}")
            
            # 文件长度 (字节24-27, 大端序, 16位字为单位)
            file_length = struct.unpack('>I', header[24:28])[0] * 2  # 转换为字节
            print(f"文件长度: {file_length} bytes")
            
            # 版本 (字节28-31, 小端序)
            version = struct.unpack('<I', header[28:32])[0]
            print(f"版本: {version}")
            
            # 几何类型 (字节32-35, 小端序)
            shape_type = struct.unpack('<I', header[32:36])[0]
            shape_types = {
                0: "Null Shape",
                1: "Point", 
                3: "Polyline",
                5: "Polygon",
                8: "MultiPoint",
                11: "PointZ",
                13: "PolylineZ", 
                15: "PolygonZ",
                18: "MultiPointZ",
                21: "PointM",
                23: "PolylineM",
                25: "PolygonM",
                28: "MultiPointM",
                31: "MultiPatch"
            }
            
            shape_name = shape_types.get(shape_type, f"Unknown ({shape_type})")
            print(f"几何类型: {shape_name}")
            
            # 边界框 (字节36-99, 小端序, 8个double值)
            bbox = struct.unpack('<8d', header[36:100])
            minx, miny, maxx, maxy = bbox[0], bbox[1], bbox[2], bbox[3]
            print(f"边界框: ({minx:.6f}, {miny:.6f}, {maxx:.6f}, {maxy:.6f})")
            
            return {
                'file_code': file_code,
                'file_length': file_length,
                'version': version,
                'shape_type': shape_type,
                'shape_name': shape_name,
                'bbox': (minx, miny, maxx, maxy)
            }
            
    except Exception as e:
        print(f"❌ 读取SHP文件头失败: {e}")
        return None

def read_prj_file(shp_path):
    """读取PRJ投影文件"""
    print(f"\n=== 分析投影信息 ===")
    
    prj_path = os.path.splitext(shp_path)[0] + '.prj'
    
    if not os.path.exists(prj_path):
        print("❌ 无投影文件 (.prj)")
        print("   这可能是导致WMS图层无法显示的主要原因！")
        return None
    
    try:
        with open(prj_path, 'r', encoding='utf-8') as f:
            prj_content = f.read().strip()
            
        print(f"✅ 投影文件存在")
        print(f"投影信息: {prj_content[:100]}...")
        
        # 简单检查常见的投影系统
        if 'GEOGCS' in prj_content:
            print("📍 地理坐标系 (Geographic Coordinate System)")
            if 'WGS_1984' in prj_content or 'WGS84' in prj_content:
                print("✅ 使用WGS84坐标系 - 适合Web地图")
            else:
                print("⚠️  使用其他地理坐标系")
        elif 'PROJCS' in prj_content:
            print("📍 投影坐标系 (Projected Coordinate System)")
            if 'Web_Mercator' in prj_content or 'WGS_1984_Web_Mercator' in prj_content:
                print("✅ 使用Web墨卡托投影 - 适合Web地图")
            else:
                print("⚠️  使用其他投影坐标系，可能影响Web地图显示")
        
        return prj_content
        
    except Exception as e:
        print(f"❌ 读取投影文件失败: {e}")
        return None

def analyze_dbf_structure(shp_path):
    """分析DBF属性文件结构"""
    print(f"\n=== 分析属性表结构 ===")
    
    dbf_path = os.path.splitext(shp_path)[0] + '.dbf'
    
    try:
        with open(dbf_path, 'rb') as f:
            # 读取DBF文件头
            header = f.read(32)
            
            # 版本信息
            version = header[0]
            print(f"DBF版本: {version}")
            
            # 记录数量 (字节4-7, 小端序)
            record_count = struct.unpack('<I', header[4:8])[0]
            print(f"记录数量: {record_count}")
            
            # 头长度 (字节8-9, 小端序)
            header_length = struct.unpack('<H', header[8:10])[0]
            print(f"头长度: {header_length} bytes")
            
            # 记录长度 (字节10-11, 小端序)
            record_length = struct.unpack('<H', header[10:12])[0]
            print(f"记录长度: {record_length} bytes")
            
            # 读取字段描述符
            field_count = (header_length - 33) // 32  # 32字节为一个字段描述符
            print(f"字段数量: {field_count}")
            
            f.seek(32)  # 跳过文件头
            print("\n字段信息:")
            for i in range(field_count):
                field_desc = f.read(32)
                if len(field_desc) < 32:
                    break
                    
                # 字段名 (11字节, null终止)
                field_name = field_desc[0:11].rstrip(b'\x00').decode('utf-8', errors='ignore')
                
                # 字段类型 (1字节)
                field_type = chr(field_desc[11])
                
                # 字段长度 (1字节)
                field_length = field_desc[16]
                
                # 小数位数 (1字节)
                decimal_count = field_desc[17]
                
                print(f"  {field_name}: {field_type} ({field_length}.{decimal_count})")
            
            return {
                'record_count': record_count,
                'field_count': field_count,
                'record_length': record_length
            }
            
    except Exception as e:
        print(f"❌ 读取DBF文件失败: {e}")
        return None

def check_coordinate_validity(bbox, shape_type):
    """检查坐标有效性"""
    print(f"\n=== 坐标有效性检查 ===")
    
    minx, miny, maxx, maxy = bbox
    issues = []
    
    # 检查边界框是否合理
    if minx >= maxx or miny >= maxy:
        issues.append("无效的边界框: 最小值大于等于最大值")
    
    # 检查是否可能是地理坐标
    if -180 <= minx <= 180 and -180 <= maxx <= 180 and -90 <= miny <= 90 and -90 <= maxy <= 90:
        print("✅ 坐标范围符合地理坐标系 (经纬度)")
        if abs(minx - maxx) < 0.001 or abs(miny - maxy) < 0.001:
            issues.append("坐标范围过小，可能存在精度问题")
    else:
        print("📍 坐标范围超出地理坐标系，可能为投影坐标系")
        if abs(minx) > 1e7 or abs(maxx) > 1e7 or abs(miny) > 1e7 or abs(maxy) > 1e7:
            issues.append("坐标值异常大，可能存在投影问题")
    
    # 检查坐标范围大小
    width = maxx - minx
    height = maxy - miny
    print(f"数据范围: 宽度={width:.6f}, 高度={height:.6f}")
    
    if issues:
        print("❌ 发现坐标问题:")
        for issue in issues:
            print(f"   {issue}")
    else:
        print("✅ 坐标看起来正常")
    
    return len(issues) == 0

def generate_wms_recommendations(shp_info, prj_content, dbf_info):
    """生成WMS兼容性建议"""
    print(f"\n=== WMS兼容性建议 ===")
    
    issues = []
    recommendations = []
    
    # 检查投影
    if prj_content is None:
        issues.append("缺少投影信息")
        recommendations.append("添加.prj文件，定义坐标参考系统")
    elif 'WGS_1984' not in prj_content and 'Web_Mercator' not in prj_content:
        issues.append("使用非标准Web地图投影")
        recommendations.append("转换为EPSG:4326 (WGS84) 或 EPSG:3857 (Web Mercator)")
    
    # 检查数据量
    if dbf_info and dbf_info['record_count'] > 10000:
        issues.append(f"数据量较大 ({dbf_info['record_count']} 条记录)")
        recommendations.append("考虑数据分片或使用矢量瓦片")
    
    # 检查几何类型
    if shp_info['shape_type'] in [31]:  # MultiPatch
        issues.append("使用复杂几何类型 (MultiPatch)")
        recommendations.append("简化几何类型或转换为标准几何")
    
    if issues:
        print("发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        
        print("\n建议的解决方案:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    else:
        print("✅ 未发现明显的WMS兼容性问题")
    
    return issues, recommendations

def main():
    print("简化 Shapefile 分析工具")
    print("=" * 50)
    print("注意: 这是不需要外部依赖的简化版本")
    print("功能有限，建议安装完整版本获得更详细的分析")
    print()
    
    # 查找shapefile
    shp_path = find_shapefile()
    if not shp_path:
        return
    
    # 检查文件完整性
    if not check_shapefile_components(shp_path):
        print("文件不完整，可能导致读取问题")
        return
    
    # 分析SHP文件头
    shp_info = read_shp_header(shp_path)
    if shp_info is None:
        return
    
    # 分析投影信息
    prj_content = read_prj_file(shp_path)
    
    # 分析DBF结构
    dbf_info = analyze_dbf_structure(shp_path)
    
    # 检查坐标有效性
    coordinate_valid = check_coordinate_validity(shp_info['bbox'], shp_info['shape_type'])
    
    # 生成WMS建议
    issues, recommendations = generate_wms_recommendations(shp_info, prj_content, dbf_info)
    
    print(f"\n=== 分析总结 ===")
    print(f"文件: {shp_path}")
    print(f"几何类型: {shp_info['shape_name']}")
    print(f"记录数: {dbf_info['record_count'] if dbf_info else '未知'}")
    print(f"坐标有效: {'是' if coordinate_valid else '否'}")
    print(f"投影完整: {'是' if prj_content else '否'}")
    
    if issues:
        print(f"\n需要关注的问题: {len(issues)} 个")
        print("建议使用完整版工具进行自动修复")
    else:
        print("\n✅ shapefile看起来符合WMS标准")
    
    print(f"\n=== 下一步操作建议 ===")
    print("1. 如果发现问题，安装完整版工具:")
    print("   python -m pip install geopandas pandas shapely fiona pyproj")
    print("   python fix_shapefile.py")
    print("2. 在Geoserver中重新发布图层")
    print("3. 检查WMS请求URL和参数")
    print("4. 测试不同客户端的显示效果")

if __name__ == "__main__":
    main() 
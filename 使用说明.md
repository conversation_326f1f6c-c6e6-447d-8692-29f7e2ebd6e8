# Shapefile 分析和修复工具使用说明

## 概述

本工具集包含两个Python脚本，用于分析和修复shapefile文件中可能导致WMS图层在Web地图组件中无法显示的问题。

## 环境准备

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install geopandas pandas shapely fiona pyproj numpy
```

### 2. 确保shapefile文件完整

Shapefile由多个文件组成，确保以下文件存在：
- **必需文件**：`.shp`、`.shx`、`.dbf`
- **重要文件**：`.prj`（投影信息）
- **可选文件**：`.cpg`、`.sbn`、`.sbx`等

## 工具使用

### 1. 分析工具 (analyze_shapefile.py)

用于诊断shapefile中的潜在问题。

```bash
python analyze_shapefile.py
```

**功能**：
- 检查文件完整性
- 分析几何类型和有效性
- 检查投影信息
- 验证坐标范围
- 评估WMS兼容性
- 提供问题诊断报告

**输出示例**：
```
=== 检查Shapefile文件组成 ===
存在的文件: ['.shp', '.shx', '.dbf']
⚠️  缺少可选文件: ['.prj']
   警告: 缺少.prj文件可能导致投影系统问题！

=== 分析 example.shp ===
✅ 成功读取shapefile
记录数量: 1500
几何类型分布:
  Polygon: 1500
❌ 无投影信息 (CRS为空)
   这可能是导致WMS图层无法显示的主要原因！
```

### 2. 修复工具 (fix_shapefile.py)

自动修复检测到的问题。

```bash
python fix_shapefile.py
```

**功能**：
- 自动备份原始文件
- 修复空几何和无效几何
- 设置或转换投影系统
- 简化复杂几何对象
- 优化属性字段
- 生成修复后的文件

**输出示例**：
```
=== 修复完成 ===
原始文件: example.shp
修复文件: example_fixed.shp
备份文件: example_backup_20241208_143025.*

建议:
1. 在Geoserver中重新发布修复后的图层
2. 测试WMS服务在不同客户端的显示效果
3. 如果问题依然存在，检查WMS请求参数和客户端配置
```

## 常见问题及解决方案

### 1. 投影系统问题

**问题**：无投影信息或使用非标准投影
**症状**：图层在Geoserver中显示正常，但在Leaflet/ArcGIS中不显示

**解决方案**：
- 工具会自动设置为EPSG:4326（WGS84）
- 如需其他投影，可修改`fix_shapefile.py`中的`target_crs`参数

### 2. 几何有效性问题

**问题**：无效的几何对象
**症状**：部分要素无法正确渲染

**解决方案**：
- 工具使用`buffer(0)`方法修复
- 无法修复的几何会被移除

### 3. 坐标范围问题

**问题**：坐标值超出有效范围
**症状**：图层位置错误或不显示

**解决方案**：
- 检查并验证投影系统是否正确
- 确认原始数据的坐标系统

### 4. 文件编码问题

**问题**：中文字段名或属性值显示乱码
**解决方案**：
- 工具保存时使用UTF-8编码
- 如仍有问题，检查`.cpg`文件

## WMS服务配置建议

### 1. Geoserver配置

```xml
<!-- 在图层配置中确保设置正确的SRS -->
<srs>EPSG:4326</srs>
<nativeBoundingBox>
  <minx>-180</minx>
  <maxx>180</maxx>
  <miny>-90</miny>
  <maxy>90</maxy>
  <crs>EPSG:4326</crs>
</nativeBoundingBox>
```

### 2. 客户端请求参数

**Leaflet示例**：
```javascript
L.tileLayer.wms('http://localhost:8080/geoserver/workspace/wms', {
    layers: 'workspace:layer_name',
    format: 'image/png',
    transparent: true,
    crs: L.CRS.EPSG4326  // 确保CRS匹配
});
```

**ArcGIS JS API示例**：
```javascript
var wmsLayer = new WMSLayer({
    url: "http://localhost:8080/geoserver/workspace/wms",
    sublayers: [{
        name: "workspace:layer_name",
        spatialReference: { wkid: 4326 }  // 确保空间参考匹配
    }]
});
```

## 高级选项

### 自定义修复参数

在`fix_shapefile.py`中可以调整以下参数：

```python
# 目标投影系统
target_crs = 'EPSG:4326'  # 或 'EPSG:3857'

# 几何简化容差
tolerance = 0.001  # 更小的值保留更多细节

# 复杂几何阈值
coord_threshold = 1000  # 坐标点数超过此值将被简化
```

### 批量处理

如需批量处理多个shapefile：

```python
import glob
for shp_file in glob.glob("*.shp"):
    # 处理每个文件
    pass
```

## 故障排除

### 1. 依赖安装失败

如果在Windows上安装geopandas遇到问题：

```bash
# 使用conda安装（推荐）
conda install geopandas

# 或使用预编译包
pip install --find-links https://girder.github.io/large_image_wheels GDAL Fiona
pip install geopandas
```

### 2. 内存不足

对于大型shapefile：
- 增加系统内存
- 使用数据分片处理
- 考虑转换为更高效的格式（如GeoPackage）

### 3. 投影转换错误

如果投影转换失败：
- 检查原始投影信息是否正确
- 尝试不同的目标投影系统
- 手动设置投影参数

## 联系支持

如果问题仍未解决，请提供：
1. 原始shapefile文件
2. 分析工具的完整输出
3. Geoserver和客户端的错误信息
4. WMS请求URL示例 
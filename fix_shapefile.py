#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Shapefile修复工具
自动修复shapefile中可能导致WMS图层显示问题的常见问题
"""

import os
import sys
import glob
import shutil
from datetime import datetime

try:
    import geopandas as gpd
    import pandas as pd
    from shapely.geometry import Point, LineString, Polygon
    from pyproj import CRS
    import numpy as np
except ImportError as e:
    print(f"缺少必要的库: {e}")
    print("请安装以下库:")
    print("pip install geopandas pandas shapely pyproj")
    sys.exit(1)

def backup_shapefile(shp_path):
    """备份原始shapefile"""
    base_name = os.path.splitext(shp_path)[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_base = f"{base_name}_backup_{timestamp}"
    
    # 需要备份的文件扩展名
    extensions = ['.shp', '.shx', '.dbf', '.prj', '.cpg', '.sbn', '.sbx', '.qix']
    
    print(f"备份原始文件到: {backup_base}.*")
    
    for ext in extensions:
        src_file = base_name + ext
        if os.path.exists(src_file):
            dst_file = backup_base + ext
            shutil.copy2(src_file, dst_file)
            print(f"  备份: {src_file} -> {dst_file}")

def fix_projection(gdf, target_crs='EPSG:4326'):
    """修复投影问题"""
    print(f"\n--- 修复投影问题 ---")
    
    if gdf.crs is None:
        print("❌ 检测到无投影信息")
        
        # 尝试根据坐标范围推断投影
        bounds = gdf.total_bounds
        minx, miny, maxx, maxy = bounds
        
        if -180 <= minx <= 180 and -180 <= maxx <= 180 and -90 <= miny <= 90 and -90 <= maxy <= 90:
            print(f"   根据坐标范围推断为地理坐标系，设置为 {target_crs}")
            gdf = gdf.set_crs(target_crs)
        else:
            print(f"   无法推断投影系统，强制设置为 {target_crs}")
            print(f"   警告: 这可能导致坐标位置错误，请手动检查!")
            gdf = gdf.set_crs(target_crs)
    
    elif gdf.crs.to_epsg() not in [4326, 3857]:
        print(f"❌ 当前投影: {gdf.crs}")
        print(f"   转换为Web友好的投影: {target_crs}")
        try:
            gdf = gdf.to_crs(target_crs)
            print("✅ 投影转换完成")
        except Exception as e:
            print(f"❌ 投影转换失败: {e}")
    
    else:
        print(f"✅ 投影系统正常: {gdf.crs}")
    
    return gdf

def fix_invalid_geometries(gdf):
    """修复无效几何"""
    print(f"\n--- 修复无效几何 ---")
    
    invalid_mask = ~gdf.geometry.is_valid
    invalid_count = invalid_mask.sum()
    
    if invalid_count > 0:
        print(f"❌ 发现 {invalid_count} 个无效几何")
        
        # 尝试使用buffer(0)修复
        print("   尝试使用buffer(0)修复...")
        gdf.loc[invalid_mask, 'geometry'] = gdf.loc[invalid_mask, 'geometry'].buffer(0)
        
        # 检查修复结果
        still_invalid = (~gdf.geometry.is_valid).sum()
        if still_invalid == 0:
            print("✅ 所有无效几何已修复")
        else:
            print(f"⚠️  仍有 {still_invalid} 个几何无法修复，将被移除")
            gdf = gdf[gdf.geometry.is_valid]
    
    else:
        print("✅ 无无效几何")
    
    return gdf

def fix_null_geometries(gdf):
    """修复空几何"""
    print(f"\n--- 修复空几何 ---")
    
    null_mask = gdf.geometry.isnull()
    null_count = null_mask.sum()
    
    if null_count > 0:
        print(f"❌ 发现 {null_count} 个空几何，将被移除")
        gdf = gdf[~null_mask]
        print("✅ 空几何已移除")
    else:
        print("✅ 无空几何")
    
    return gdf

def simplify_complex_geometries(gdf, tolerance=0.001):
    """简化复杂几何"""
    print(f"\n--- 简化复杂几何 ---")
    
    # 检查几何复杂度
    complex_count = 0
    simplified_count = 0
    
    for idx, geom in gdf.geometry.items():
        if geom is None:
            continue
            
        # 计算坐标点数
        coord_count = 0
        if hasattr(geom, 'coords'):
            coord_count = len(list(geom.coords))
        elif hasattr(geom, 'exterior'):
            coord_count = len(list(geom.exterior.coords))
        
        if coord_count > 1000:
            complex_count += 1
            # 简化几何
            simplified_geom = geom.simplify(tolerance, preserve_topology=True)
            gdf.loc[idx, 'geometry'] = simplified_geom
            simplified_count += 1
    
    if complex_count > 0:
        print(f"❌ 发现 {complex_count} 个复杂几何")
        print(f"✅ 已简化 {simplified_count} 个几何 (tolerance={tolerance})")
    else:
        print("✅ 无复杂几何需要简化")
    
    return gdf

def validate_coordinate_bounds(gdf):
    """验证坐标范围"""
    print(f"\n--- 验证坐标范围 ---")
    
    bounds = gdf.total_bounds
    minx, miny, maxx, maxy = bounds
    
    print(f"当前边界: ({minx:.6f}, {miny:.6f}, {maxx:.6f}, {maxy:.6f})")
    
    issues = []
    
    if gdf.crs and gdf.crs.is_geographic:
        if minx < -180 or maxx > 180:
            issues.append(f"经度超出范围: [{minx:.6f}, {maxx:.6f}]")
        if miny < -90 or maxy > 90:
            issues.append(f"纬度超出范围: [{miny:.6f}, {maxy:.6f}]")
    
    # 检查是否有异常大的坐标值
    if abs(minx) > 1e6 or abs(maxx) > 1e6 or abs(miny) > 1e6 or abs(maxy) > 1e6:
        issues.append("检测到异常大的坐标值，可能存在投影问题")
    
    if issues:
        print("❌ 坐标范围问题:")
        for issue in issues:
            print(f"   {issue}")
    else:
        print("✅ 坐标范围正常")
    
    return len(issues) == 0

def optimize_attributes(gdf):
    """优化属性字段"""
    print(f"\n--- 优化属性字段 ---")
    
    # 移除完全为空的列
    empty_cols = []
    for col in gdf.columns:
        if col != gdf.geometry.name and gdf[col].isnull().all():
            empty_cols.append(col)
    
    if empty_cols:
        print(f"移除空列: {empty_cols}")
        gdf = gdf.drop(columns=empty_cols)
    
    # 优化数据类型
    for col in gdf.columns:
        if col != gdf.geometry.name:
            # 尝试转换为更合适的数据类型
            if gdf[col].dtype == 'object':
                # 尝试转换为数值类型
                try:
                    pd.to_numeric(gdf[col], errors='raise')
                    gdf[col] = pd.to_numeric(gdf[col], errors='coerce')
                    print(f"列 '{col}' 转换为数值类型")
                except:
                    pass
    
    print("✅ 属性字段优化完成")
    return gdf

def save_fixed_shapefile(gdf, output_path):
    """保存修复后的shapefile"""
    print(f"\n--- 保存修复后的文件 ---")
    
    try:
        gdf.to_file(output_path, driver='ESRI Shapefile', encoding='utf-8')
        print(f"✅ 文件已保存: {output_path}")
        
        # 显示保存后的统计信息
        print(f"   记录数: {len(gdf)}")
        print(f"   字段数: {len(gdf.columns)}")
        print(f"   投影系统: {gdf.crs}")
        
        return True
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False

def main():
    print("Shapefile 修复工具")
    print("=" * 50)
    
    # 查找shapefile
    shp_files = glob.glob("*.shp")
    if not shp_files:
        print("❌ 在当前目录中未找到.shp文件")
        return
    
    shp_path = shp_files[0]
    print(f"处理文件: {shp_path}")
    
    try:
        # 读取原始文件
        print("\n=== 读取原始文件 ===")
        gdf = gpd.read_file(shp_path)
        print(f"✅ 成功读取 {len(gdf)} 条记录")
        
        # 备份原始文件
        backup_shapefile(shp_path)
        
        # 应用修复
        print(f"\n=== 开始修复 ===")
        
        # 1. 修复空几何
        gdf = fix_null_geometries(gdf)
        
        # 2. 修复无效几何
        gdf = fix_invalid_geometries(gdf)
        
        # 3. 修复投影问题
        gdf = fix_projection(gdf)
        
        # 4. 简化复杂几何
        gdf = simplify_complex_geometries(gdf)
        
        # 5. 验证坐标范围
        validate_coordinate_bounds(gdf)
        
        # 6. 优化属性字段
        gdf = optimize_attributes(gdf)
        
        # 生成输出文件名
        base_name = os.path.splitext(shp_path)[0]
        output_path = f"{base_name}_fixed.shp"
        
        # 保存修复后的文件
        if save_fixed_shapefile(gdf, output_path):
            print(f"\n=== 修复完成 ===")
            print(f"原始文件: {shp_path}")
            print(f"修复文件: {output_path}")
            print(f"备份文件: {base_name}_backup_*.* ")
            print("\n建议:")
            print("1. 在Geoserver中重新发布修复后的图层")
            print("2. 测试WMS服务在不同客户端的显示效果")
            print("3. 如果问题依然存在，检查WMS请求参数和客户端配置")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 
<!DOCTYPE html>
<html>
<head>
    <title>WMS图层测试 - Leaflet</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        body { 
            margin: 0; 
            padding: 20px; 
            font-family: Arial, sans-serif; 
        }
        #map { 
            height: 600px; 
            width: 100%; 
            border: 2px solid #ccc;
            margin-top: 10px;
        }
        .info-panel {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .coordinates {
            font-family: monospace;
            background: #e8e8e8;
            padding: 5px;
            display: inline-block;
            border-radius: 3px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>WMS图层测试 - grideLine</h1>
    
    <div class="info-panel">
        <h3>📍 数据信息</h3>
        <p><strong>服务地址:</strong> http://*************:9709/geoserver/jc/wms</p>
        <p><strong>图层名称:</strong> jc:grideLine</p>
        <p><strong>数据范围:</strong> <span class="coordinates">112.960142°E - 112.961070°E, 27.287127°N - 27.287724°N</span></p>
        <p><strong>数据大小:</strong> 约100米 × 65米 (需要高缩放级别才能看到)</p>
    </div>
    
    <div class="info-panel">
        <h3>🎛️ 控制面板</h3>
        <button onclick="goToDataBounds()">📍 缩放到数据范围</button>
        <button onclick="setHighZoom()">🔍 设置高缩放级别 (zoom=18)</button>
        <button onclick="toggleWMSLayer()">👁️ 切换WMS图层</button>
        <button onclick="showCurrentView()">📊 显示当前视图信息</button>
        <button onclick="testWMSUrl()">🌐 测试WMS URL</button>
    </div>
    
    <div id="status" class="status warning">
        <strong>状态:</strong> 地图初始化中...
    </div>
    
    <div id="map"></div>
    
    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // 数据边界
        const DATA_BOUNDS = L.latLngBounds(
            [27.287127, 112.960142],  // 西南角
            [27.287724, 112.961070]   // 东北角
        );
        
        const DATA_CENTER = [27.2874255, 112.960606];
        
        // 初始化地图
        const map = L.map('map', {
            crs: L.CRS.EPSG4326,  // 使用地理坐标系
            maxZoom: 20           // 允许高缩放级别
        });
        
        // 添加OpenStreetMap底图
        const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 19
        }).addTo(map);
        
        // WMS图层配置
        const wmsLayer = L.tileLayer.wms('http://*************:9709/geoserver/jc/wms', {
            layers: 'jc:gridLine',
            format: 'image/png',
            transparent: true,
            crs: L.CRS.EPSG4326,
            maxZoom: 20,
            attribution: 'GeoServer WMS - grideLine'
        });
        
        // 添加WMS图层
        wmsLayer.addTo(map);
        
        // 添加数据范围边界框
        const boundsRectangle = L.rectangle(DATA_BOUNDS, {
            color: 'red',
            weight: 2,
            fillOpacity: 0.1
        }).addTo(map);
        
        // 添加中心点标记
        const centerMarker = L.marker(DATA_CENTER).addTo(map)
            .bindPopup('数据中心点<br>需要zoom≥16才能看到线条');
        
        // 初始视图设置
        map.fitBounds(DATA_BOUNDS);
        
        // 状态显示函数
        function updateStatus(message, type = 'warning') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>状态:</strong> ${message}`;
        }
        
        // 监听地图事件
        map.on('zoomend moveend', function() {
            const zoom = map.getZoom();
            const center = map.getCenter();
            const bounds = map.getBounds();
            
            let message = `缩放级别: ${zoom}, 中心: [${center.lat.toFixed(6)}, ${center.lng.toFixed(6)}]`;
            
            if (zoom < 16) {
                message += ' - ⚠️ 缩放级别太低，可能看不到数据';
                updateStatus(message, 'warning');
            } else {
                message += ' - ✅ 缩放级别合适';
                updateStatus(message, 'success');
            }
        });
        
        // WMS图层加载事件
        wmsLayer.on('loading', function() {
            updateStatus('WMS图层加载中...', 'warning');
        });
        
        wmsLayer.on('load', function() {
            const zoom = map.getZoom();
            if (zoom >= 16) {
                updateStatus('WMS图层加载完成 ✅', 'success');
            } else {
                updateStatus('WMS图层已加载，但可能因缩放级别太低而不可见', 'warning');
            }
        });
        
        wmsLayer.on('tileerror', function(e) {
            updateStatus('WMS图层加载失败 ❌ 检查网络连接和服务状态', 'error');
            console.error('WMS瓦片加载错误:', e);
        });
        
        // 控制函数
        function goToDataBounds() {
            map.fitBounds(DATA_BOUNDS);
            updateStatus('已缩放到数据范围', 'success');
        }
        
        function setHighZoom() {
            map.setView(DATA_CENTER, 18);
            updateStatus('已设置高缩放级别 (zoom=18)', 'success');
        }
        
        function toggleWMSLayer() {
            if (map.hasLayer(wmsLayer)) {
                map.removeLayer(wmsLayer);
                updateStatus('WMS图层已隐藏', 'warning');
            } else {
                map.addLayer(wmsLayer);
                updateStatus('WMS图层已显示', 'success');
            }
        }
        
        function showCurrentView() {
            const zoom = map.getZoom();
            const center = map.getCenter();
            const bounds = map.getBounds();
            
            alert(`当前视图信息:
缩放级别: ${zoom}
中心点: [${center.lat.toFixed(6)}, ${center.lng.toFixed(6)}]
边界: [${bounds.getSouth().toFixed(6)}, ${bounds.getWest().toFixed(6)}, ${bounds.getNorth().toFixed(6)}, ${bounds.getEast().toFixed(6)}]
数据可见性: ${zoom >= 16 ? '可见' : '不可见(缩放级别太低)'}`);
        }
        
        function testWMSUrl() {
            const testUrl = 'http://*************:9709/geoserver/jc/wms?' +
                'SERVICE=WMS&VERSION=1.1.0&REQUEST=GetMap&' +
                'LAYERS=jc:grideLine&' +
                'BBOX=112.96014175961582,27.287126649206925,112.9610703536677,27.28772356870276&' +
                'WIDTH=512&HEIGHT=512&SRS=EPSG:4326&FORMAT=image/png&TRANSPARENT=true';
            
            window.open(testUrl, '_blank');
            updateStatus('已在新窗口打开WMS测试URL', 'success');
        }
        
        // 初始状态
        updateStatus('地图初始化完成，WMS图层已添加', 'success');
        
        // 添加图层控制
        const baseLayers = {
            "OpenStreetMap": osmLayer
        };
        
        const overlayLayers = {
            "WMS图层 (grideLine)": wmsLayer,
            "数据边界": boundsRectangle,
            "中心点": centerMarker
        };
        
        L.control.layers(baseLayers, overlayLayers).addTo(map);
        
        // 添加比例尺
        L.control.scale().addTo(map);
        
        console.log('地图初始化完成');
        console.log('数据范围:', DATA_BOUNDS);
        console.log('如果看不到线条，请尝试缩放到zoom level 16或更高');
    </script>
</body>
</html> 
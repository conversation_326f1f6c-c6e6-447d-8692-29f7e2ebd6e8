#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Shapefile分析工具
分析shapefile文件的结构、投影、几何类型等信息，找出可能导致WMS图层显示问题的原因
"""

import os
import sys
import glob
try:
    import geopandas as gpd
    import pandas as pd
    from shapely.geometry import Point, LineString, Polygon
    import fiona
    from pyproj import CRS
    import numpy as np
except ImportError as e:
    print(f"缺少必要的库: {e}")
    print("请安装以下库:")
    print("pip install geopandas pandas shapely fiona pyproj")
    sys.exit(1)

def find_shapefile():
    """查找当前目录下的shapefile文件"""
    shp_files = glob.glob("*.shp")
    if not shp_files:
        print("在当前目录中未找到.shp文件")
        return None
    
    print(f"找到shapefile文件: {shp_files}")
    return shp_files[0]  # 返回第一个找到的文件

def check_shapefile_components(shp_path):
    """检查shapefile的组成文件是否完整"""
    print("\n=== 检查Shapefile文件组成 ===")
    base_name = os.path.splitext(shp_path)[0]
    
    required_files = ['.shp', '.shx', '.dbf']
    optional_files = ['.prj', '.cpg', '.sbn', '.sbx', '.qix']
    
    missing_required = []
    missing_optional = []
    existing_files = []
    
    for ext in required_files:
        file_path = base_name + ext
        if os.path.exists(file_path):
            existing_files.append(ext)
        else:
            missing_required.append(ext)
    
    for ext in optional_files:
        file_path = base_name + ext
        if os.path.exists(file_path):
            existing_files.append(ext)
        else:
            missing_optional.append(ext)
    
    print(f"存在的文件: {existing_files}")
    if missing_required:
        print(f"❌ 缺少必需文件: {missing_required}")
        return False
    else:
        print("✅ 必需文件完整")
    
    if missing_optional:
        print(f"⚠️  缺少可选文件: {missing_optional}")
        if '.prj' in missing_optional:
            print("   警告: 缺少.prj文件可能导致投影系统问题！")
    
    return True

def analyze_shapefile(shp_path):
    """分析shapefile的详细信息"""
    print(f"\n=== 分析 {shp_path} ===")
    
    try:
        # 使用geopandas读取
        gdf = gpd.read_file(shp_path)
        print(f"✅ 成功读取shapefile")
        
        # 基本信息
        print(f"\n--- 基本信息 ---")
        print(f"记录数量: {len(gdf)}")
        print(f"字段数量: {len(gdf.columns)}")
        print(f"几何列名: {gdf.geometry.name}")
        
        # 几何类型分析
        print(f"\n--- 几何类型分析 ---")
        geom_types = gdf.geometry.geom_type.value_counts()
        print("几何类型分布:")
        for geom_type, count in geom_types.items():
            print(f"  {geom_type}: {count}")
        
        # 检查空几何
        null_geoms = gdf.geometry.isnull().sum()
        if null_geoms > 0:
            print(f"❌ 发现 {null_geoms} 个空几何对象")
        else:
            print("✅ 无空几何对象")
        
        # 检查无效几何
        invalid_geoms = (~gdf.geometry.is_valid).sum()
        if invalid_geoms > 0:
            print(f"❌ 发现 {invalid_geoms} 个无效几何对象")
            # 显示无效几何的详细信息
            invalid_mask = ~gdf.geometry.is_valid
            for idx in gdf[invalid_mask].index[:5]:  # 只显示前5个
                geom = gdf.loc[idx, 'geometry']
                print(f"   索引 {idx}: {geom.is_valid_reason if hasattr(geom, 'is_valid_reason') else '无效几何'}")
        else:
            print("✅ 无无效几何对象")
        
        # 坐标范围分析
        print(f"\n--- 坐标范围分析 ---")
        bounds = gdf.total_bounds
        print(f"边界框 (minx, miny, maxx, maxy): {bounds}")
        
        # 检查坐标值是否合理
        minx, miny, maxx, maxy = bounds
        if abs(minx) > 180 or abs(maxx) > 180:
            print("⚠️  X坐标超出经度范围 (-180, 180)")
        if abs(miny) > 90 or abs(maxy) > 90:
            print("⚠️  Y坐标超出纬度范围 (-90, 90)")
        
        # 投影信息分析
        print(f"\n--- 投影信息分析 ---")
        crs = gdf.crs
        if crs is None:
            print("❌ 无投影信息 (CRS为空)")
            print("   这可能是导致WMS图层无法显示的主要原因！")
        else:
            print(f"投影系统: {crs}")
            print(f"EPSG代码: {crs.to_epsg() if crs.to_epsg() else '未知'}")
            print(f"是否为地理坐标系: {crs.is_geographic}")
            print(f"是否为投影坐标系: {crs.is_projected}")
            
            # 检查常见的Web地图投影
            if crs.to_epsg() == 4326:
                print("✅ 使用WGS84地理坐标系 (EPSG:4326) - 适合Web地图")
            elif crs.to_epsg() == 3857:
                print("✅ 使用Web墨卡托投影 (EPSG:3857) - 适合Web地图")
            else:
                print("⚠️  使用非标准Web地图投影，可能导致显示问题")
        
        # 属性字段分析
        print(f"\n--- 属性字段分析 ---")
        print("字段信息:")
        for col in gdf.columns:
            if col != gdf.geometry.name:
                dtype = gdf[col].dtype
                null_count = gdf[col].isnull().sum()
                print(f"  {col}: {dtype} (null: {null_count})")
        
        # 使用fiona获取更详细的信息
        print(f"\n--- 详细格式信息 (fiona) ---")
        with fiona.open(shp_path) as src:
            print(f"驱动程序: {src.driver}")
            print(f"CRS: {src.crs}")
            print(f"边界: {src.bounds}")
            print(f"模式: {src.schema}")
        
        return gdf
        
    except Exception as e:
        print(f"❌ 读取shapefile时出错: {e}")
        return None

def check_wms_compatibility(gdf):
    """检查WMS兼容性"""
    print(f"\n=== WMS兼容性检查 ===")
    
    issues = []
    
    # 1. 投影系统检查
    if gdf.crs is None:
        issues.append("缺少投影信息 - WMS服务需要明确的坐标参考系统")
    elif gdf.crs.to_epsg() not in [4326, 3857]:
        issues.append(f"投影系统 {gdf.crs.to_epsg()} 可能不被所有Web地图客户端支持")
    
    # 2. 几何有效性检查
    if (~gdf.geometry.is_valid).any():
        issues.append("存在无效几何 - 可能导致渲染失败")
    
    # 3. 坐标范围检查
    bounds = gdf.total_bounds
    minx, miny, maxx, maxy = bounds
    
    if gdf.crs and gdf.crs.is_geographic:
        if minx < -180 or maxx > 180 or miny < -90 or maxy > 90:
            issues.append("坐标超出有效经纬度范围")
    
    # 4. 数据量检查
    if len(gdf) > 10000:
        issues.append(f"数据量较大 ({len(gdf)} 条记录) - 可能影响WMS性能")
    
    # 5. 几何复杂度检查
    if not gdf.empty:
        geom_sample = gdf.geometry.iloc[0]
        if hasattr(geom_sample, 'coords'):
            coord_count = len(list(geom_sample.coords))
            if coord_count > 1000:
                issues.append("几何对象过于复杂 - 可能影响渲染性能")
    
    if issues:
        print("发现的潜在问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    else:
        print("✅ 未发现明显的WMS兼容性问题")
    
    return issues

def suggest_solutions(issues):
    """根据发现的问题提供解决方案"""
    if not issues:
        return
    
    print(f"\n=== 建议的解决方案 ===")
    
    solutions = {
        "缺少投影信息": "使用 gdf.set_crs('EPSG:4326') 设置投影系统",
        "投影系统": "转换为Web友好的投影系统: gdf.to_crs('EPSG:4326') 或 gdf.to_crs('EPSG:3857')",
        "无效几何": "使用 gdf.geometry = gdf.geometry.buffer(0) 修复几何问题",
        "坐标超出": "检查并清理坐标数据，确保在有效范围内",
        "数据量较大": "考虑数据分片或使用矢量瓦片服务",
        "几何对象过于复杂": "使用 gdf.geometry = gdf.geometry.simplify(tolerance) 简化几何"
    }
    
    for issue in issues:
        for key, solution in solutions.items():
            if key in issue:
                print(f"• {issue}")
                print(f"  解决方案: {solution}")
                break

def main():
    print("Shapefile 分析工具")
    print("=" * 50)
    
    # 查找shapefile
    shp_path = find_shapefile()
    if not shp_path:
        return
    
    # 检查文件完整性
    if not check_shapefile_components(shp_path):
        print("文件不完整，可能导致读取问题")
        return
    
    # 分析shapefile
    gdf = analyze_shapefile(shp_path)
    if gdf is None:
        return
    
    # 检查WMS兼容性
    issues = check_wms_compatibility(gdf)
    
    # 提供解决方案
    suggest_solutions(issues)
    
    print(f"\n=== 分析完成 ===")
    print("如果问题依然存在，请检查:")
    print("1. Geoserver的图层配置")
    print("2. WMS请求参数 (SRS/CRS, BBOX等)")
    print("3. 客户端的投影系统支持")
    print("4. 网络连接和服务器配置")

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMS服务测试工具
测试不同的WMS请求参数和格式，帮助诊断客户端显示问题
"""

import urllib.request
import urllib.parse
import os
import time

# 基础服务信息
BASE_URL = "http://192.168.1.154:9709/geoserver/jc/wms"
LAYER_NAME = "jc:grideLine"
BBOX = "112.96014175961582,27.287126649206925,112.9610703536677,27.28772356870276"

def test_wms_request(params, test_name, output_file=None):
    """测试WMS请求"""
    print(f"\n=== 测试: {test_name} ===")
    
    # 构建完整URL
    url_params = urllib.parse.urlencode(params, quote_via=urllib.parse.quote)
    full_url = f"{BASE_URL}?{url_params}"
    
    print(f"请求URL: {full_url}")
    
    try:
        # 发送请求
        with urllib.request.urlopen(full_url, timeout=10) as response:
            content_type = response.headers.get('Content-Type', 'unknown')
            content_length = response.headers.get('Content-Length', 'unknown')
            
            print(f"响应状态: {response.status}")
            print(f"Content-Type: {content_type}")
            print(f"Content-Length: {content_length}")
            
            # 读取响应内容
            content = response.read()
            
            # 如果指定了输出文件，保存内容
            if output_file:
                with open(output_file, 'wb') as f:
                    f.write(content)
                print(f"响应已保存到: {output_file}")
            
            # 分析响应
            if content_type.startswith('image/'):
                print(f"✅ 成功返回图像，大小: {len(content)} bytes")
                return True
            elif content_type.startswith('text/html'):
                print(f"✅ 返回HTML预览页面，大小: {len(content)} bytes")
                return True
            elif content_type.startswith('application/vnd.ogc'):
                # OGC服务异常
                error_text = content.decode('utf-8', errors='ignore')[:200]
                print(f"❌ OGC服务异常: {error_text}")
                return False
            else:
                # 检查是否是错误响应
                text_content = content.decode('utf-8', errors='ignore')[:200]
                if 'error' in text_content.lower() or 'exception' in text_content.lower():
                    print(f"❌ 错误响应: {text_content}")
                    return False
                else:
                    print(f"⚠️  未知响应类型: {text_content}")
                    return False
                    
    except urllib.error.HTTPError as e:
        print(f"❌ HTTP错误: {e.code} {e.reason}")
        try:
            error_content = e.read().decode('utf-8', errors='ignore')[:200]
            print(f"错误详情: {error_content}")
        except:
            pass
        return False
    except urllib.error.URLError as e:
        print(f"❌ URL错误: {e.reason}")
        return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    print("WMS服务测试工具")
    print("=" * 60)
    print(f"服务地址: {BASE_URL}")
    print(f"图层名称: {LAYER_NAME}")
    print(f"数据范围: {BBOX}")
    
    # 测试用例
    test_cases = [
        {
            "name": "1. GetCapabilities - 获取服务能力",
            "params": {
                "SERVICE": "WMS",
                "REQUEST": "GetCapabilities"
            },
            "output": "capabilities.xml"
        },
        {
            "name": "2. 原始请求 - OpenLayers预览格式",
            "params": {
                "service": "WMS",
                "version": "1.1.0", 
                "request": "GetMap",
                "layers": LAYER_NAME,
                "bbox": BBOX,
                "width": "768",
                "height": "493", 
                "srs": "EPSG:4326",
                "styles": "",
                "format": "application/openlayers"
            },
            "output": "test1_openlayers.html"
        },
        {
            "name": "3. PNG图像格式",
            "params": {
                "SERVICE": "WMS",
                "VERSION": "1.1.0",
                "REQUEST": "GetMap", 
                "LAYERS": LAYER_NAME,
                "BBOX": BBOX,
                "WIDTH": "512",
                "HEIGHT": "512",
                "SRS": "EPSG:4326",
                "FORMAT": "image/png",
                "TRANSPARENT": "true"
            },
            "output": "test2_image.png"
        },
        {
            "name": "4. WMS 1.3.0版本 (使用CRS)",
            "params": {
                "SERVICE": "WMS", 
                "VERSION": "1.3.0",
                "REQUEST": "GetMap",
                "LAYERS": LAYER_NAME,
                "BBOX": BBOX,
                "WIDTH": "512",
                "HEIGHT": "512", 
                "CRS": "EPSG:4326",
                "FORMAT": "image/png",
                "TRANSPARENT": "true"
            },
            "output": "test3_wms13.png"
        },
        {
            "name": "5. JPEG格式",
            "params": {
                "SERVICE": "WMS",
                "VERSION": "1.1.0", 
                "REQUEST": "GetMap",
                "LAYERS": LAYER_NAME,
                "BBOX": BBOX,
                "WIDTH": "512",
                "HEIGHT": "512",
                "SRS": "EPSG:4326", 
                "FORMAT": "image/jpeg"
            },
            "output": "test4_jpeg.jpg"
        },
        {
            "name": "6. 小尺寸图像 (256x256)",
            "params": {
                "SERVICE": "WMS",
                "VERSION": "1.1.0",
                "REQUEST": "GetMap",
                "LAYERS": LAYER_NAME, 
                "BBOX": BBOX,
                "WIDTH": "256", 
                "HEIGHT": "256",
                "SRS": "EPSG:4326",
                "FORMAT": "image/png",
                "TRANSPARENT": "true"
            },
            "output": "test5_small.png"
        },
        {
            "name": "7. 大尺寸图像 (1024x1024)",
            "params": {
                "SERVICE": "WMS", 
                "VERSION": "1.1.0",
                "REQUEST": "GetMap",
                "LAYERS": LAYER_NAME,
                "BBOX": BBOX,
                "WIDTH": "1024",
                "HEIGHT": "1024", 
                "SRS": "EPSG:4326",
                "FORMAT": "image/png",
                "TRANSPARENT": "true"
            },
            "output": "test6_large.png"
        },
        {
            "name": "8. 扩大边界框测试",
            "params": {
                "SERVICE": "WMS",
                "VERSION": "1.1.0", 
                "REQUEST": "GetMap",
                "LAYERS": LAYER_NAME,
                "BBOX": "112.95,27.28,112.97,27.29",  # 扩大边界
                "WIDTH": "512",
                "HEIGHT": "512",
                "SRS": "EPSG:4326",
                "FORMAT": "image/png", 
                "TRANSPARENT": "true"
            },
            "output": "test7_expanded.png"
        }
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for test_case in test_cases:
        success = test_wms_request(
            test_case["params"], 
            test_case["name"],
            test_case.get("output")
        )
        if success:
            success_count += 1
        
        time.sleep(0.5)  # 避免请求过于频繁
    
    print(f"\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_count} 个测试通过")
    
    if success_count == total_count:
        print("✅ WMS服务工作正常！")
        print("\n客户端配置建议:")
        print("1. 确保使用正确的BBOX范围")
        print("2. 设置适当的缩放级别 (建议zoom >= 16)")
        print("3. 确保CRS/SRS参数正确")
        print("4. 检查图层可见性和样式设置")
    else:
        print("⚠️  部分测试失败，请检查服务配置")
    
    print(f"\n生成的测试文件:")
    for test_case in test_cases:
        output_file = test_case.get("output")
        if output_file and os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"  {output_file}: {file_size} bytes")

if __name__ == "__main__":
    main() 